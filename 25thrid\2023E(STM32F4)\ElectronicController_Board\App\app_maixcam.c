// Copyright (c) 2024 �״׵��ӹ�����

#include "app_maixcam.h"
#include <string.h> // for strncmp
#include <stdio.h>  // for sscanf

// ������Щ��Ԥ����ĺ��ö��
// #define RED_LASER_ID  'R' // ���������㶨������ڱ�ʾ��to�����ݵ�ID
// #define GREEN_LASER_ID 'G' // ���������㶨������ڱ�ʾ��pur�����ݵ�ID

// Ĭ�ϻص�����
static void default_laser_callback(LaserCoord_t coord)
{
    // �����µ�����ӳ������������
    if (coord.type == RED_LASER_ID) // ����ӳ�䵽 "to:(X,Y)" ���� (Ŀ���)
    {
        // ��һ�ζ�ȡ������ֵ��Ŀ��㣩�����ʼ��PID������Ŀ��
        // ע�⣺�����app_pid_init()��app_pid_start()���ÿ�յ�һ��Ŀ���͵��ã�
        // ���ܻᵼ��PIDƵ����λ������������ͨ����Щֻ��ϵͳ������Ŀ���״�ȷ��ʱ����һ�Ρ�
        // �������Ҫ����һ����־λ��ȷ��ֻ��ʼ��һ�Ρ�
        // ���磺
        // static bool pid_initialized = false;
        // if (!pid_initialized) {
        //     app_pid_init();
        //     app_pid_start();
        //     pid_initialized = true;
        // }
        
        app_pid_init(); // ���app_pid_init���ݵȵģ�������ϣ��ÿ���յ�Ŀ�궼���ã�������
        app_pid_set_target(coord.x, coord.y); // ����������X,Y����ΪĿ��λ��
        my_printf(&huart1, "Target: X=%d, Y=%d\r\n", coord.x, coord.y);
        app_pid_start(); // ���app_pid_start���ݵȵģ�������ϣ��ÿ���յ�Ŀ�궼���ã�������
    }
    else if (coord.type == GREEN_LASER_ID) // ����ӳ�䵽 "pur:(X,Y)" ���� (ʵ�ʼ����)
    {
        // �������ͨ����PID�ĵ�ǰ����λ��
        my_printf(&huart1, "Laser: X=%d, Y=%d\r\n", coord.x, coord.y);
        // ������PIDϵͳ��Ҫʵʱ���µ�ǰ����λ�ã����������������Ӧ�ĺ��������磺
        // app_pid_update_current_position(coord.x, coord.y);
    }
    else
    {
        // δ֪���ͣ��������ڵ���
        my_printf(&huart1, "Unknown type: %c, X=%d, Y=%d\r\n", coord.type, coord.x, coord.y);
    }
}

// �ص�����ָ�룬Ĭ��ָ��Ĭ�ϻص�����
static LaserCoordCallback_t laser_coord_callback = default_laser_callback;

// �����������ݻص�����
void maixcam_set_callback(LaserCoordCallback_t callback)
{
    if (callback != NULL)
        laser_coord_callback = callback;
    else
        laser_coord_callback = default_laser_callback;
}

// MaixCam ���ݽ�������
// ������ʽ��to:(X,Y) �� pur:(X,Y)
int maixcam_parse_data(char *buffer)
{
    if (!buffer)
        return -1; // ������Ϊ��

    LaserCoord_t coord;
    int parsed_count;

    // ���Խ��� "to:(?X,Y)" ��ʽ
    // ����Ƿ��� "to:(" ��ͷ
    if (strncmp(buffer, "to:(", 4) == 0)
    {
        // �� "to:(" ֮��ʼ�������ڴ� %d,%d)
        parsed_count = sscanf(buffer + 4, "%d,%d)", &coord.x, &coord.y);
        if (parsed_count == 2)
        {
            coord.type = RED_LASER_ID; // ӳ�� "to" ����Ϊ��ɫ����ID
            if (laser_coord_callback)
                laser_coord_callback(coord);
            return 0; // �ɹ�����
        }
    }
    // ���Խ��� "pur:(X,Y)" ��ʽ
    // ����Ƿ��� "pur:(" ��ͷ
    else if (strncmp(buffer, "pur:(", 5) == 0)
    {
        // �� "pur:(" ֮��ʼ�������ڴ� %d,%d)
        parsed_count = sscanf(buffer + 5, "%d,%d)", &coord.x, &coord.y);
        if (parsed_count == 2)
        {
            coord.type = GREEN_LASER_ID; // ӳ�� "pur" ����Ϊ��ɫ����ID
            if (laser_coord_callback)
                laser_coord_callback(coord);
            return 0; // �ɹ�����
        }
    }

    // �����ƥ���κ���֪��ʽ
    return -2; // δ֪��ʽ�����ʧ��
}

// MaixCam ���ݽ���������
void maixcam_task(MultiTimer *timer, void *userData)
{
    // ���� ringbuffer_cam �� output_buffer_cam ����ȷ����ͳ�ʼ��
    // ���磺extern rt_ringbuffer_t ringbuffer_cam; extern uint8_t output_buffer_cam[BUFFER_SIZE];

    int length_cam = rt_ringbuffer_data_len(&ringbuffer_cam);
    if (length_cam > 0)
    {
        // �ӻ��λ�������ȡ���ݵ���ʱ������
        // ȷ�� output_buffer_cam �㹻���������������֡
        rt_ringbuffer_get(&ringbuffer_cam, output_buffer_cam, length_cam);
        output_buffer_cam[length_cam] = '\0'; // ȷ���ַ����Կ��ַ���β

        int result = maixcam_parse_data((char *)output_buffer_cam);
        if (result != 0) {
            // ���Ը���result��ӡ������Ϣ�����ڵ���
            // my_printf(&huart1, "Parse error: %d, Data: %s\r\n", result, output_buffer_cam);
        }

        // ��ջ�����������ʵ�ʻ��λ�������ʹ�÷�ʽ�����п��ܲ���Ҫ���в�ͬʵ�֣�
        memset(output_buffer_cam, 0, length_cam);
    }

    // multiTimerStart(&mt_pid, 10, app_pid_task, NULL); // ���б��ֲ��䣬PIDͨ���ڶ�������������
}
