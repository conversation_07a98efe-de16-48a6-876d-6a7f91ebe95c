{"version": "0.2.0", "configurations": [{"name": "C/C++ Runner: Debug Session", "type": "cppdbg", "request": "launch", "args": [], "stopAtEntry": false, "externalConsole": true, "cwd": "d:/Desktop/25thridyizhi/25thrid/2023E(STM32F4)/ElectronicController_Board/App", "program": "d:/Desktop/25thridyizhi/25thrid/2023E(STM32F4)/ElectronicController_Board/App/build/Debug/outDebug", "MIMode": "gdb", "miDebuggerPath": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}]}]}